import { chatHistoryService } from './storage/chatHistoryDexie'
import { platformService } from './storage/platformDexie'
import { dexieDatabase } from './database/dexie'
import { runE2ETest } from '../test/e2e-test'

export class DebugHelper {
  /**
   * 输出数据库当前状态
   */
  async logDatabaseState(): Promise<void> {
    console.group('📊 数据库状态')
    
    try {
      // 平台信息
      const platformsResult = await platformService.getList()
      if (platformsResult.success) {
        console.log('🏢 平台列表:')
        platformsResult.data.forEach(platform => {
          console.log(`  - ${platform.name} (ID: ${platform.id}): ${platform.url}`)
        })
      }
      
      // 聊天历史统计
      const historyResult = await chatHistoryService.getList({ limit: 100 })
      if (historyResult.success) {
        console.log(`📜 聊天历史: 共 ${historyResult.data.total} 条记录`)
        
        // 按平台统计
        const platformStats = new Map<number, number>()
        historyResult.data.data.forEach(record => {
          const count = platformStats.get(record.platform_id) || 0
          platformStats.set(record.platform_id, count + 1)
        })
        
        console.log('📊 平台分布:')
        platformStats.forEach((count, platformId) => {
          console.log(`  - 平台 ${platformId}: ${count} 条`)
        })
      }
      
      // 去重统计
      const uniqueResult = await chatHistoryService.getUniqueChats({ limit: 50 })
      if (uniqueResult.success) {
        console.log(`🔗 去重后: ${uniqueResult.data.length} 个唯一提示词`)
      }
      
    } catch (error) {
      console.error('❌ 获取数据库状态失败:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 模拟用户操作流程
   */
  async simulateUserFlow(): Promise<void> {
    console.group('🎭 模拟用户操作流程')
    
    const testPrompts = [
      "如何学习JavaScript？",
      "什么是React Hooks？",
      "TypeScript的优势是什么？"
    ]
    
    try {
      for (let i = 0; i < testPrompts.length; i++) {
        const prompt = testPrompts[i]
        console.log(`\n步骤 ${i + 1}: 在DeepSeek发送提示词`)
        
        // 模拟在DeepSeek发送
        const deepseekResult = await chatHistoryService.create({
          chat_prompt: prompt,
          chat_uid: `sim-${Date.now()}-${i}`,
          platform_id: 1,
          create_time: Date.now()
        })
        
        if (deepseekResult.success) {
          console.log(`✅ DeepSeek存储成功: ${prompt}`)
          
          // 延迟一下，然后在Kimi复用
          await new Promise(resolve => setTimeout(resolve, 100))
          
          console.log(`步骤 ${i + 1}.1: 在Kimi复用相同提示词`)
          const kimiResult = await chatHistoryService.create({
            chat_prompt: prompt, // 相同提示词
            chat_uid: `kimi-${Date.now()}-${i}`,
            platform_id: 2,
            create_time: Date.now()
          })
          
          if (kimiResult.success) {
            console.log(`✅ Kimi存储成功，UID: ${kimiResult.data.chat_uid}`)
            
            // 检查是否复用了UID
            if (kimiResult.data.chat_uid === deepseekResult.data.chat_uid) {
              console.log('🔗 UID复用成功！')
            } else {
              console.log('⚠️  UID未复用（可能是首次使用）')
            }
          } else {
            console.error(`❌ Kimi存储失败: ${kimiResult.error}`)
          }
        } else {
          console.error(`❌ DeepSeek存储失败: ${deepseekResult.error}`)
        }
      }
      
    } catch (error) {
      console.error('❌ 模拟用户流程失败:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 验证数据完整性
   */
  async validateDataIntegrity(): Promise<boolean> {
    console.group('🔍 数据完整性验证')
    
    try {
      const result = await chatHistoryService.getList({ limit: 1000 })
      if (!result.success) {
        console.error('❌ 获取数据失败:', result.error)
        console.groupEnd()
        return false
      }
      
      const records = result.data.data
      const uidToPromptMap = new Map<string, string>()
      const duplicateUids: string[] = []
      
      // 检查UID和提示词的一致性
      for (const record of records) {
        const existingPrompt = uidToPromptMap.get(record.chat_uid)
        if (existingPrompt) {
          if (existingPrompt !== record.chat_prompt) {
            duplicateUids.push(record.chat_uid)
            console.error(`❌ UID冲突: ${record.chat_uid}`)
            console.error(`  - 已存在: ${existingPrompt}`)
            console.error(`  - 当前: ${record.chat_prompt}`)
          }
        } else {
          uidToPromptMap.set(record.chat_uid, record.chat_prompt)
        }
      }
      
      // 检查平台ID的有效性
      const invalidPlatformIds: number[] = []
      for (const record of records) {
        const platformResult = await platformService.getById(record.platform_id)
        if (!platformResult.success) {
          invalidPlatformIds.push(record.platform_id)
        }
      }
      
      const isValid = duplicateUids.length === 0 && invalidPlatformIds.length === 0
      
      if (isValid) {
        console.log('✅ 数据完整性验证通过')
        console.log(`  - 总记录数: ${records.length}`)
        console.log(`  - 唯一UID数: ${uidToPromptMap.size}`)
      } else {
        console.error('❌ 数据完整性验证失败')
        if (duplicateUids.length > 0) {
          console.error(`  - UID冲突数: ${duplicateUids.length}`)
        }
        if (invalidPlatformIds.length > 0) {
          console.error(`  - 无效平台ID: ${[...new Set(invalidPlatformIds)].join(', ')}`)
        }
      }
      
      console.groupEnd()
      return isValid
      
    } catch (error) {
      console.error('❌ 数据完整性验证异常:', error)
      console.groupEnd()
      return false
    }
  }

  /**
   * 清理测试数据
   */
  async cleanupTestData(): Promise<void> {
    console.group('🧹 清理测试数据')
    
    try {
      const result = await chatHistoryService.getList({ limit: 1000 })
      if (!result.success) {
        console.error('❌ 获取数据失败:', result.error)
        console.groupEnd()
        return
      }
      
      const testRecords = result.data.data.filter(record => 
        record.chat_prompt.includes('测试') || 
        record.chat_uid.includes('test-') ||
        record.chat_uid.includes('sim-')
      )
      
      let deletedCount = 0
      for (const record of testRecords) {
        const deleteResult = await chatHistoryService.delete(record.id!)
        if (deleteResult.success) {
          deletedCount++
        }
      }
      
      console.log(`✅ 清理完成，删除了 ${deletedCount} 条测试记录`)
      
    } catch (error) {
      console.error('❌ 清理测试数据失败:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 运行完整的调试检查
   */
  async runFullCheck(): Promise<void> {
    console.log('🚀 开始完整调试检查...')
    
    // 1. 数据库状态
    await this.logDatabaseState()
    
    // 2. 数据完整性
    const isValid = await this.validateDataIntegrity()
    
    // 3. 如果数据完整，运行端到端测试
    if (isValid) {
      console.log('\n🧪 运行端到端测试...')
      await runE2ETest()
    } else {
      console.log('\n⚠️  数据完整性问题，跳过端到端测试')
    }
    
    console.log('\n✅ 调试检查完成')
  }
}

// 导出单例实例
export const debugHelper = new DebugHelper()

// 全局调试方法（可在控制台直接调用）
declare global {
  interface Window {
    echoSyncDebug: {
      logState: () => Promise<void>
      simulate: () => Promise<void>
      validate: () => Promise<boolean>
      cleanup: () => Promise<void>
      fullCheck: () => Promise<void>
      runTest: () => Promise<any>
    }
  }
}

// 注册全局调试方法
if (typeof window !== 'undefined') {
  window.echoSyncDebug = {
    logState: () => debugHelper.logDatabaseState(),
    simulate: () => debugHelper.simulateUserFlow(),
    validate: () => debugHelper.validateDataIntegrity(),
    cleanup: () => debugHelper.cleanupTestData(),
    fullCheck: () => debugHelper.runFullCheck(),
    runTest: () => runE2ETest()
  }
  
  console.log('🔧 EchoSync调试工具已加载，使用 window.echoSyncDebug 访问调试方法')
}
