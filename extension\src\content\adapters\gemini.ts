import { AIAdapter } from '../base'
import { Conversation, Message, AIPlatform } from '@/types'

export class GeminiAdapter extends AIAdapter {
  constructor() {
    const platform = {
      name: 'Gemini',
      id: 'gemini' as AIPlatform,
      url: 'https://gemini.google.com'
    }

    const selectors = {
      inputField: '.ql-editor, [contenteditable="true"]',
      sendButton: 'button[aria-label*="Send"], button[type="submit"]',
      messageContainer: '.conversation-container, .message'
    }

    super(platform, selectors)
  }



  async injectPrompt(prompt: string): Promise<void> {
    // 使用输入管理器注入提示词
    this.inputManager.injectPrompt(prompt)
  }

  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = document.querySelectorAll(this.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []
      
      messageElements.forEach((element, index) => {
        // Gemini的消息结构可能比较复杂
        const isUser = element.querySelector('[data-test-id="user-message"]') !== null ||
                      element.classList.contains('user-message') ||
                      element.getAttribute('data-message-author') === 'user'

        const contentElement = element.querySelector('[data-test-id="message-content"]') || 
                              element.querySelector('.message-content') || 
                              element.querySelector('.markdown-content') ||
                              element

        if (contentElement) {
          const content = contentElement.textContent?.trim() || ''
          if (content && content.length > 0) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      const title = `Gemini对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `gemini-${Date.now()}`,
        platform: 'gemini',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('Extract Gemini conversation error:', error)
      return null
    }
  }

  isValidPage(): boolean {
    return window.location.hostname === 'gemini.google.com'
  }
}
