/**
 * 拖拽处理类
 * 从base.ts中提取的拖拽功能相关方法
 */
export class DragHandler {
  private bubble: HTMLElement | null = null
  private dragState = {
    isDragging: false,
    isLongPressing: false,
    dragStartTime: 0,
    dragStartX: 0,
    dragStartY: 0,
    bubbleStartX: 0,
    bubbleStartY: 0,
    longPressTimer: null as number | null,
    dragThreshold: 5
  }

  constructor(bubble: HTMLElement) {
    this.bubble = bubble
    this.setupDragEvents()
  }

  /**
   * 设置拖拽事件
   */
  private setupDragEvents(): void {
    if (!this.bubble) return

    // 鼠标事件
    this.bubble.addEventListener('mousedown', this.handleMouseDown.bind(this))
    document.addEventListener('mousemove', this.handleMouseMove.bind(this))
    document.addEventListener('mouseup', this.handleMouseUp.bind(this))

    // 触摸事件
    this.bubble.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false })
    document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false })
    document.addEventListener('touchend', this.handleTouchEnd.bind(this))

    console.log('Drag events set up for floating bubble')
  }

  /**
   * 鼠标按下事件
   */
  private handleMouseDown(e: MouseEvent): void {
    this.handleDragStart(e.clientX, e.clientY)
  }

  /**
   * 触摸开始事件
   */
  private handleTouchStart(e: TouchEvent): void {
    e.preventDefault()
    const touch = e.touches[0]
    this.handleDragStart(touch.clientX, touch.clientY)
  }

  /**
   * 开始拖拽
   */
  private handleDragStart(clientX: number, clientY: number): void {
    if (!this.bubble) return

    this.dragState.dragStartTime = Date.now()
    this.dragState.dragStartX = clientX
    this.dragState.dragStartY = clientY
    this.dragState.isLongPressing = true

    const rect = this.bubble.getBoundingClientRect()
    this.dragState.bubbleStartX = rect.left
    this.dragState.bubbleStartY = rect.top

    // 立即添加轻微的按下效果
    this.bubble.style.transform = 'scale(0.95)'
    this.bubble.style.transition = 'transform 0.1s ease'

    // 设置长按定时器（300ms后开始拖拽模式，更快响应）
    this.dragState.longPressTimer = window.setTimeout(() => {
      if (this.dragState.isLongPressing) {
        this.dragState.isDragging = true
        this.startDragMode()
      }
    }, 300)

    // 添加视觉反馈
    this.bubble.style.cursor = 'grabbing'

    console.log('Drag start initiated')
  }

  /**
   * 鼠标移动事件
   */
  private handleMouseMove(e: MouseEvent): void {
    this.handleDragMove(e.clientX, e.clientY)
  }

  /**
   * 触摸移动事件
   */
  private handleTouchMove(e: TouchEvent): void {
    e.preventDefault()
    const touch = e.touches[0]
    this.handleDragMove(touch.clientX, touch.clientY)
  }

  /**
   * 拖拽移动
   */
  private handleDragMove(clientX: number, clientY: number): void {
    if (!this.bubble) return

    const deltaX = clientX - this.dragState.dragStartX
    const deltaY = clientY - this.dragState.dragStartY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    // 如果还在长按状态，检查是否超过拖拽阈值
    if (this.dragState.isLongPressing && !this.dragState.isDragging && distance > this.dragState.dragThreshold) {
      // 取消长按定时器，立即进入拖拽模式
      if (this.dragState.longPressTimer) {
        clearTimeout(this.dragState.longPressTimer)
        this.dragState.longPressTimer = null
      }
      this.dragState.isDragging = true
      this.startDragMode()
    }

    // 只有在拖拽模式下才移动气泡
    if (!this.dragState.isDragging) return

    const newX = this.dragState.bubbleStartX + deltaX
    const newY = this.dragState.bubbleStartY + deltaY

    // 边界检查，增加弹性效果
    const bubbleSize = 80 // 拖拽时的大小
    const margin = 10 // 边界缓冲
    const maxX = window.innerWidth - bubbleSize + margin
    const maxY = window.innerHeight - bubbleSize + margin

    let constrainedX = Math.max(-margin, Math.min(newX, maxX))
    let constrainedY = Math.max(-margin, Math.min(newY, maxY))

    // 添加边界弹性效果
    if (newX < 0) constrainedX = newX * 0.3
    if (newX > window.innerWidth - bubbleSize) constrainedX = (window.innerWidth - bubbleSize) + (newX - (window.innerWidth - bubbleSize)) * 0.3
    if (newY < 0) constrainedY = newY * 0.3
    if (newY > window.innerHeight - bubbleSize) constrainedY = (window.innerHeight - bubbleSize) + (newY - (window.innerHeight - bubbleSize)) * 0.3

    this.bubble.style.left = `${constrainedX}px`
    this.bubble.style.top = `${constrainedY}px`
    this.bubble.style.right = 'auto'
    this.bubble.style.transition = 'none' // 拖拽时禁用过渡动画
  }

  /**
   * 鼠标释放事件
   */
  private handleMouseUp(): void {
    this.handleDragEnd()
  }

  /**
   * 触摸结束事件
   */
  private handleTouchEnd(): void {
    this.handleDragEnd()
  }

  /**
   * 结束拖拽
   */
  private handleDragEnd(): void {
    this.dragState.isLongPressing = false

    if (this.dragState.longPressTimer) {
      clearTimeout(this.dragState.longPressTimer)
      this.dragState.longPressTimer = null
    }

    if (this.bubble) {
      // 如果没有进入拖拽模式，恢复按下效果
      if (!this.dragState.isDragging) {
        this.bubble.style.transform = 'scale(1)'
        this.bubble.style.transition = 'transform 0.2s ease'
      }
    }

    if (this.dragState.isDragging) {
      this.endDragMode()

      // 边界回弹效果
      document.dispatchEvent(new CustomEvent('echosync:snap-to-boundary'))

      // 延迟重置拖拽状态，避免立即触发点击事件
      setTimeout(() => {
        this.dragState.isDragging = false
      }, 150)
    }

    if (this.bubble) {
      this.bubble.style.cursor = 'pointer'
    }

    console.log('Drag end')
  }

  /**
   * 开始拖拽模式
   */
  private startDragMode(): void {
    if (!this.bubble) return

    // 拖拽时的视觉效果 - 更加动态和现代
    this.bubble.style.transform = 'scale(1.2)'
    this.bubble.style.boxShadow = '0 12px 48px rgba(139, 92, 246, 0.4), 0 4px 16px rgba(0, 0, 0, 0.2)'
    this.bubble.style.zIndex = '10001'
    this.bubble.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease'

    // 添加轻微的脉冲效果
    this.bubble.style.animation = 'echosync-pulse 1.5s ease-in-out infinite'

    // 添加脉冲动画样式
    if (!document.getElementById('echosync-drag-styles')) {
      const style = document.createElement('style')
      style.id = 'echosync-drag-styles'
      style.textContent = `
        @keyframes echosync-pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.8; }
        }
      `
      document.head.appendChild(style)
    }

    console.log('Drag mode started')
  }

  /**
   * 结束拖拽模式
   */
  private endDragMode(): void {
    if (!this.bubble) return

    // 恢复原始大小和效果
    this.bubble.style.transform = 'scale(1)'
    this.bubble.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)'
    this.bubble.style.zIndex = '10000'
    this.bubble.style.transition = 'all 0.3s ease'
    this.bubble.style.animation = 'none'

    console.log('Drag mode ended')
  }

  /**
   * 获取拖拽状态
   */
  getDragState(): typeof this.dragState {
    return { ...this.dragState }
  }

  /**
   * 是否正在拖拽
   */
  isDragging(): boolean {
    return this.dragState.isDragging
  }

  /**
   * 销毁
   */
  destroy(): void {
    if (this.dragState.longPressTimer) {
      clearTimeout(this.dragState.longPressTimer)
      this.dragState.longPressTimer = null
    }

    // 移除事件监听器
    if (this.bubble) {
      this.bubble.removeEventListener('mousedown', this.handleMouseDown.bind(this))
      this.bubble.removeEventListener('touchstart', this.handleTouchStart.bind(this))
    }

    document.removeEventListener('mousemove', this.handleMouseMove.bind(this))
    document.removeEventListener('mouseup', this.handleMouseUp.bind(this))
    document.removeEventListener('touchmove', this.handleTouchMove.bind(this))
    document.removeEventListener('touchend', this.handleTouchEnd.bind(this))

    this.bubble = null
  }
}
