import { MessagingService } from '../lib/messaging'
import { StorageService } from '../lib/storage'
import { MessageType, ChromeMessage, Prompt } from '../types'

console.log('EchoSync Background Script loaded')

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('EchoSync installed:', details.reason)
  
  if (details.reason === 'install') {
    // 首次安装，设置默认配置
    const settings = await StorageService.getSettings()
    await StorageService.saveSettings(settings)
    
    // 打开欢迎页面
    chrome.tabs.create({
      url: chrome.runtime.getURL('options/index.html?welcome=true')
    })
  }
})

// 监听来自popup和content script的消息
MessagingService.onMessage(async (message, sender, sendResponse) => {
  console.log('Background received message:', message.type, message.payload)

  try {
    switch (message.type) {
      case MessageType.SYNC_PROMPT:
        await handleSyncPrompt(message.payload, sender)
        sendResponse({ success: true })
        break

      case MessageType.GET_HISTORY:
        const prompts = await StorageService.getPrompts()
        sendResponse({ success: true, data: prompts })
        break

      case MessageType.SAVE_CONVERSATION:
        await StorageService.addConversation(message.payload)
        sendResponse({ success: true })
        break

      case MessageType.UPDATE_SETTINGS:
        await StorageService.saveSettings(message.payload)
        sendResponse({ success: true })
        break

      case MessageType.CAPTURE_PROMPT:
        await handleCapturePrompt(message.payload, sender)
        sendResponse({ success: true })
        break

      default:
        sendResponse({ success: false, error: 'Unknown message type' })
    }
  } catch (error) {
    console.error('Background message handler error:', error)
    sendResponse({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    })
  }
})

// 处理提示词同步
async function handleSyncPrompt(promptData, sender) {
  const prompt = {
    id: Date.now().toString(36) + Math.random().toString(36).substr(2),
    content: promptData.content,
    platform: promptData.platform,
    timestamp: Date.now(),
    tags: promptData.tags || [],
    isFavorite: false,
    category: promptData.category
  }

  // 保存提示词
  await StorageService.addPrompt(prompt)

  // 获取设置，检查是否启用实时同步
  const settings = await StorageService.getSettings()
  if (!settings.syncEnabled) return

  // 获取所有支持的标签页
  const tabs = await chrome.tabs.query({
    url: [
      'https://chat.openai.com/*',
      'https://chat.deepseek.com/*',
      'https://claude.ai/*',
      'https://gemini.google.com/*'
    ]
  })

  // 向其他标签页发送同步消息
  const syncPromises = tabs
    .filter(tab => tab.id !== sender.tab?.id) // 排除发送者标签页
    .map(tab => {
      if (tab.id) {
        return MessagingService.sendToContentScript(
          tab.id,
          MessageType.INJECT_PROMPT,
          { prompt: prompt.content }
        ).catch(error => {
          console.log(`Failed to sync to tab ${tab.id}:`, error)
        })
      }
    })

  await Promise.all(syncPromises)
}

// 处理提示词捕获
async function handleCapturePrompt(data, sender) {
  const prompt = {
    id: Date.now().toString(36) + Math.random().toString(36).substr(2),
    content: data.content,
    platform: data.platform,
    timestamp: Date.now(),
    tags: [],
    isFavorite: false
  }

  await StorageService.addPrompt(prompt)
  
  // 通知popup更新
  try {
    chrome.runtime.sendMessage({
      type: 'PROMPT_CAPTURED',
      payload: prompt
    })
  } catch (error) {
    // Popup可能未打开，忽略错误
  }
}

// 监听标签页更新，检测AI平台
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    const supportedDomains = [
      'chat.openai.com',
      'chat.deepseek.com',
      'claude.ai',
      'gemini.google.com'
    ]

    const isSupported = supportedDomains.some(domain => tab.url && tab.url.includes(domain))
    
    if (isSupported) {
      // 注入content script（如果需要）
      try {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['content/index.js']
        })
      } catch (error) {
        // 可能已经注入过了，忽略错误
      }
    }
  }
})

// 处理快捷键
chrome.commands.onCommand.addListener(async (command) => {
  console.log('Command received:', command)
  
  switch (command) {
    case 'open-popup':
      // 打开popup（通过点击图标实现）
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tab.id) {
        chrome.action.openPopup()
      }
      break
      
    case 'quick-sync':
      // 快速同步当前页面的提示词
      const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (currentTab.id && await MessagingService.isTabSupported(currentTab.id)) {
        MessagingService.sendToContentScript(
          currentTab.id,
          MessageType.CAPTURE_PROMPT,
          {}
        )
      }
      break
  }
})

// 保持service worker活跃
chrome.runtime.onStartup.addListener(() => {
  console.log('EchoSync service worker started')
})

// 定期清理过期数据
setInterval(async () => {
  try {
    const prompts = await StorageService.getPrompts()
    const oneMonthAgo = Date.now() - (30 * 24 * 60 * 60 * 1000)
    
    // 保留最近一个月的数据
    const recentPrompts = prompts.filter(p => p.timestamp > oneMonthAgo)
    
    if (recentPrompts.length !== prompts.length) {
      await StorageService.savePrompts(recentPrompts)
      console.log(`Cleaned up ${prompts.length - recentPrompts.length} old prompts`)
    }
  } catch (error) {
    console.error('Cleanup error:', error)
  }
}, 24 * 60 * 60 * 1000) // 每24小时执行一次
