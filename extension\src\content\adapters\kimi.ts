import { AIAdapter } from '../base'
import { Conversation, Message, AIPlatform } from '@/types'

export class <PERSON><PERSON><PERSON><PERSON>pt<PERSON> extends AIAdapter {
  constructor() {
    console.log('KimiAdapter constructor called')

    const platform = {
      name: '<PERSON><PERSON>',
      id: 'kimi' as AIPlatform,
      url: 'https://kimi.moonshot.cn'
    }

    const selectors = {
      inputField: '.chat-input-editor[contenteditable="true"], [data-lexical-editor="true"], .chat-input-editor, [contenteditable="true"]',
      sendButton: '.send-button, .send-button-container button, [class*="send-button"]',
      messageContainer: '.chat-content-item, .segment'
    }

    super(platform, selectors)
    console.log('KimiAdapter initialized with selectors:', selectors)
  }

  async injectPrompt(prompt: string): Promise<void> {
    // 使用输入管理器注入提示词
    this.inputManager.injectPrompt(prompt)
  }

  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = document.querySelectorAll(this.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []

      messageElements.forEach((element, index) => {
        // Kimi使用特定的类名来区分用户和助手消息
        const isUser = element.classList.contains('chat-content-item-user') ||
                      element.classList.contains('segment-user') ||
                      element.querySelector('.user-content') !== null

        const isAssistant = element.classList.contains('chat-content-item-assistant') ||
                           element.classList.contains('segment-assistant') ||
                           element.querySelector('.markdown-container') !== null

        // 只处理用户或助手消息
        if (!isUser && !isAssistant) return

        const contentElement = element.querySelector('.user-content') ||
                              element.querySelector('.markdown-container .markdown') ||
                              element.querySelector('.segment-content-box') ||
                              element

        if (contentElement) {
          const content = contentElement.textContent?.trim() || ''
          if (content) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      // 获取对话标题
      const titleElement = document.querySelector('.chat-header-content h2, .chat-name')
      const title = titleElement?.textContent?.trim() || `Kimi对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `kimi-${Date.now()}`,
        platform: 'kimi',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('Extract Kimi conversation error:', error)
      return null
    }
  }

  isValidPage(): boolean {
    const hostname = window.location.hostname
    const isValid = hostname.includes('kimi.moonshot.cn') || hostname.includes('kimi.com')
    console.log('KimiAdapter.isValidPage() - hostname:', hostname, 'isValid:', isValid)
    return isValid
  }

  // 重写发送按钮检测，适配Kimi的特殊结构
  protected findSendButton(): HTMLElement | null {
    const selectors = [
      '.send-button',
      '.send-button-container .send-button',
      '.send-button-container button',
      '.chat-editor-action .send-button-container button',
      '[class*="send-button"]'
    ]

    for (const selector of selectors) {
      const button = document.querySelector(selector) as HTMLElement
      if (button && !button.classList.contains('disabled')) {
        return button
      }
    }

    return null
  }


}
