import { useState, useCallback, useEffect } from 'react'
import { platformService } from '../lib/storage/platformDexie'
import {
  Platform,
  CreatePlatformInput,
  UpdatePlatformInput
} from '../types/database'

export interface UsePlatformState {
  platforms: Platform[]
  platformsWithChats: Platform[]
  loading: boolean
  error: string | null
}

export interface UsePlatformActions {
  createPlatform: (input: CreatePlatformInput) => Promise<Platform | null>
  updatePlatform: (id: number, input: UpdatePlatformInput) => Promise<Platform | null>
  deletePlatform: (id: number) => Promise<boolean>
  getPlatformById: (id: number) => Promise<Platform | null>
  getPlatformByName: (name: string) => Promise<Platform | null>
  findPlatformByUrl: (url: string) => Promise<Platform | null>
  loadPlatforms: () => Promise<void>
  loadPlatformsWithChats: () => Promise<void>
  refresh: () => Promise<void>
  clearError: () => void
}

export interface UsePlatformReturn extends UsePlatformState, UsePlatformActions {}

/**
 * 平台管理Hook
 */
export function usePlatform(): UsePlatformReturn {
  const [state, setState] = useState<UsePlatformState>({
    platforms: [],
    platformsWithChats: [],
    loading: false,
    error: null
  })

  /**
   * 创建平台
   */
  const createPlatform = useCallback(async (input: CreatePlatformInput): Promise<Platform | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await platformService.create(input)
      
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          loading: false,
          platforms: [...prev.platforms, result.data!]
        }))
        return result.data
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to create platform' 
        }))
        return null
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create platform'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return null
    }
  }, [])

  /**
   * 更新平台
   */
  const updatePlatform = useCallback(async (id: number, input: UpdatePlatformInput): Promise<Platform | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await platformService.update(id, input)
      
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          loading: false,
          platforms: prev.platforms.map(platform => 
            platform.id === id ? result.data! : platform
          )
        }))
        return result.data
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to update platform' 
        }))
        return null
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update platform'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return null
    }
  }, [])

  /**
   * 删除平台
   */
  const deletePlatform = useCallback(async (id: number): Promise<boolean> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await platformService.delete(id)
      
      if (result.success) {
        setState(prev => ({
          ...prev,
          loading: false,
          platforms: prev.platforms.filter(platform => platform.id !== id),
          platformsWithChats: prev.platformsWithChats.filter(platform => platform.id !== id)
        }))
        return true
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to delete platform' 
        }))
        return false
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete platform'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return false
    }
  }, [])

  /**
   * 根据ID获取平台
   */
  const getPlatformById = useCallback(async (id: number): Promise<Platform | null> => {
    try {
      const result = await platformService.getById(id)
      return result.success ? result.data || null : null
    } catch (error) {
      console.error('Failed to get platform by id:', error)
      return null
    }
  }, [])

  /**
   * 根据名称获取平台
   */
  const getPlatformByName = useCallback(async (name: string): Promise<Platform | null> => {
    try {
      const result = await platformService.getByName(name)
      return result.success ? result.data || null : null
    } catch (error) {
      console.error('Failed to get platform by name:', error)
      return null
    }
  }, [])

  /**
   * 根据URL查找平台
   */
  const findPlatformByUrl = useCallback(async (url: string): Promise<Platform | null> => {
    try {
      const hostname = new URL(url).hostname
      const result = await platformService.findByDomain(hostname)
      return result.success ? result.data || null : null
    } catch (error) {
      console.error('Failed to find platform by URL:', error)
      return null
    }
  }, [])

  /**
   * 加载所有平台
   */
  const loadPlatforms = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await platformService.getAll()
      
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          loading: false,
          platforms: result.data || []
        }))
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to load platforms' 
        }))
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load platforms'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
    }
  }, [])

  /**
   * 加载有聊天记录的平台
   */
  const loadPlatformsWithChats = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const result = await platformService.getAll()
      
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          loading: false,
          platformsWithChats: result.data || []
        }))
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: result.error || 'Failed to load platforms with chats' 
        }))
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load platforms with chats'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
    }
  }, [])

  /**
   * 刷新数据
   */
  const refresh = useCallback(async (): Promise<void> => {
    await Promise.all([
      loadPlatforms(),
      loadPlatformsWithChats()
    ])
  }, [loadPlatforms, loadPlatformsWithChats])

  /**
   * 清除错误
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  /**
   * 初始化加载数据
   */
  useEffect(() => {
    loadPlatforms()
    loadPlatformsWithChats()
  }, [])

  return {
    platforms: state.platforms,
    platformsWithChats: state.platformsWithChats,
    loading: state.loading,
    error: state.error,
    createPlatform,
    updatePlatform,
    deletePlatform,
    getPlatformById,
    getPlatformByName,
    findPlatformByUrl,
    loadPlatforms,
    loadPlatformsWithChats,
    refresh,
    clearError
  }
}

/**
 * 平台图标缓存Hook
 */
export function usePlatformIcons() {
  const [iconCache, setIconCache] = useState<Map<string, string>>(new Map())
  const [loading, setLoading] = useState<Set<string>>(new Set())

  /**
   * 获取平台图标
   */
  const getIcon = useCallback(async (platform: Platform): Promise<string> => {
    const cacheKey = `${platform.id}-${platform.name}`
    
    // 检查缓存
    if (iconCache.has(cacheKey)) {
      return iconCache.get(cacheKey)!
    }

    // 检查是否正在加载
    if (loading.has(cacheKey)) {
      return platform.icon || ''
    }

    // 开始加载
    setLoading(prev => new Set(prev).add(cacheKey))

    try {
      let iconUrl = platform.icon || ''
      
      // 如果没有图标URL，尝试获取favicon
      if (!iconUrl) {
        iconUrl = `${platform.url}/favicon.ico`
      }

      // 验证图标是否可访问
      const response = await fetch(iconUrl, { method: 'HEAD' })
      if (!response.ok) {
        iconUrl = '' // 使用默认图标
      }

      // 更新缓存
      setIconCache(prev => new Map(prev).set(cacheKey, iconUrl))
      
      return iconUrl
    } catch (error) {
      console.error('Failed to load platform icon:', error)
      return ''
    } finally {
      setLoading(prev => {
        const newSet = new Set(prev)
        newSet.delete(cacheKey)
        return newSet
      })
    }
  }, [iconCache, loading])

  /**
   * 预加载图标
   */
  const preloadIcons = useCallback(async (platforms: Platform[]): Promise<void> => {
    const promises = platforms.map(platform => getIcon(platform))
    await Promise.allSettled(promises)
  }, [getIcon])

  /**
   * 清除缓存
   */
  const clearCache = useCallback(() => {
    setIconCache(new Map())
    setLoading(new Set())
  }, [])

  return {
    getIcon,
    preloadIcons,
    clearCache,
    isLoading: (platform: Platform) => loading.has(`${platform.id}-${platform.name}`)
  }
}
