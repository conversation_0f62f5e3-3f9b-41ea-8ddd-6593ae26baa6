import { MessagingService } from '@/lib/messaging'
import { MessageType, ChromeMessage, AIPlatform } from '@/types'
import { ChatGPTAdapter } from './adapters/chatgpt'
import { DeepSeekAdapter } from './adapters/deepseek'
import { ClaudeAdapter } from './adapters/claude'
import { GeminiAdapter } from './adapters/gemini'
import { KimiAdapter } from './adapters/kimi'
import { AIAdapter } from './base'

console.log('EchoSync Content Script loaded on:', window.location.href)
console.log('Current hostname:', window.location.hostname)
console.log('User agent:', navigator.userAgent)

class ContentScriptManager {
  private adapter?: AIAdapter;
  private isInitialized: boolean = false;

  constructor() {
    this.adapter = null
    this.isInitialized = false
    this.init()
  }
  async init() {
    if (this.isInitialized) return
    
    // 检测当前平台并初始化适配器
    this.adapter = this.detectPlatform()
    
    if (this.adapter) {
      console.log('Detected platform:', this.adapter.getPlatformName())

      // 初始化适配器的通用功能（悬浮气泡、存档按钮等）
      await this.adapter.initUniversalFeatures()

      await this.setupEventListeners()
      this.isInitialized = true
    }
  }

  detectPlatform() {
    const hostname = window.location.hostname
    console.log('Detecting platform for hostname:', hostname)

    switch (hostname) {
      case 'chat.openai.com':
        console.log('Detected ChatGPT platform')
        return new ChatGPTAdapter()
      case 'chat.deepseek.com':
        console.log('Detected DeepSeek platform')
        return new DeepSeekAdapter()
      case 'claude.ai':
        console.log('Detected Claude platform')
        return new ClaudeAdapter()
      case 'gemini.google.com':
        console.log('Detected Gemini platform')
        return new GeminiAdapter()
      default:
        // 检查是否是Kimi的域名
        console.log('Checking Kimi domains...')
        console.log('hostname.includes("kimi.moonshot.cn"):', hostname.includes('kimi.moonshot.cn'))
        console.log('hostname.includes("kimi.com"):', hostname.includes('kimi.com'))
        if (hostname.includes('kimi.moonshot.cn') || hostname.includes('kimi.com')) {
          console.log('Detected Kimi platform')
          return new KimiAdapter()
        }
        console.log('No platform detected for hostname:', hostname)
        return null
    }
  }

  async setupEventListeners() {
    if (!this.adapter) return

    // 监听来自background的消息
    MessagingService.onMessage(async (message, sender, sendResponse) => {
      try {
        switch (message.type) {
          case MessageType.INJECT_PROMPT:
            await this.injectPrompt(message.payload.prompt)
            sendResponse({ success: true })
            break

          case MessageType.CAPTURE_PROMPT:
            const prompt = await this.captureCurrentPrompt()
            if (prompt) {
              await MessagingService.sendToBackground(MessageType.CAPTURE_PROMPT, {
                content: prompt,
                platform: this.adapter && this.adapter.getPlatformName().toLowerCase()
              })
            }
            sendResponse({ success: true, data: prompt })
            break

          default:
            sendResponse({ success: false, error: 'Unknown message type' })
        }
      } catch (error) {
        console.error('Content script message handler error:', error)
        sendResponse({ 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        })
      }
    })

    // 监听输入框的变化，自动捕获提示词
    this.setupPromptCapture()
  }

  setupPromptCapture() {
    if (!this.adapter) return

    let lastPrompt = ''
    let captureTimeout: string | number | NodeJS.Timeout | null | undefined = null

    const capturePrompt = async () => {
      try {
        const inputElement = this.adapter && document.querySelector(this.adapter.getSelectors().inputField)
        if (!inputElement) return

        let currentPrompt = ''
        
        // 处理不同类型的输入元素
        if (inputElement.tagName === 'TEXTAREA' || inputElement.tagName === 'INPUT') {
          currentPrompt = (inputElement as HTMLInputElement | HTMLTextAreaElement).value
        } else if ((inputElement as HTMLElement).contentEditable === 'true') {
          currentPrompt = (inputElement as HTMLElement).textContent || ''
        }

        // 只有当提示词发生变化且不为空时才捕获
        if (currentPrompt && currentPrompt !== lastPrompt && currentPrompt.length > 10) {
          lastPrompt = currentPrompt
          
          // 发送到background script
          await MessagingService.sendToBackground(MessageType.SYNC_PROMPT, {
            content: currentPrompt,
            platform: this.adapter && this.adapter.getPlatformName().toLowerCase(),
            timestamp: Date.now()
          })
        }
      } catch (error) {
        console.error('Capture prompt error:', error)
      }
    }

    // 监听输入事件
    document.addEventListener('input', (event) => {
      const target = event.target
      if (this.adapter && target && (target as Element).matches(this.adapter.getSelectors().inputField)) {
        // 防抖处理，避免频繁触发
        if (captureTimeout) clearTimeout(captureTimeout)
        captureTimeout = setTimeout(capturePrompt, 1000)
      }
    })

    // 监听发送按钮点击
    document.addEventListener('click', (event) => {
      const target = event.target
      if (this.adapter && ((target as Element).matches(this.adapter.getSelectors().sendButton) ||
        (target as Element).closest(this.adapter.getSelectors().sendButton))) {
        // 立即捕获提示词
        setTimeout(capturePrompt, 100)
      }
    })
  }

  async injectPrompt(prompt: string) {
    if (!this.adapter) return

    try {
      await this.adapter.injectPrompt(prompt)
      
      // 显示注入成功的提示
      this.showNotification('提示词已同步', 'success')
    } catch (error) {
      console.error('Inject prompt error:', error)
      this.showNotification('提示词同步失败', 'error')
    }
  }

  async captureCurrentPrompt() {
    if (!this.adapter) return null

    try {
      const inputElement = document.querySelector(this.adapter.getSelectors().inputField)
      if (!inputElement) return null

      if (inputElement.tagName === 'TEXTAREA' || inputElement.tagName === 'INPUT') {
        return (inputElement as HTMLInputElement | HTMLTextAreaElement).value
      } else if ((inputElement as HTMLElement).contentEditable === 'true') {
        return inputElement.textContent || ''
      }

      return null
    } catch (error) {
      console.error('Capture current prompt error:', error)
      return null
    }
  }

  showNotification(message: string | null, type = 'success') {
    // 创建通知元素
    const notification = document.createElement('div')
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      border-radius: 8px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      background-color: ${type === 'success' ? '#10b981' : '#ef4444'};
    `
    notification.textContent = message

    document.body.appendChild(notification)

    // 3秒后自动移除
    setTimeout(() => {
      notification.style.opacity = '0'
      notification.style.transform = 'translateX(100%)'
      setTimeout(() => {
        document.body.removeChild(notification)
      }, 300)
    }, 3000)
  }
}

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new ContentScriptManager()
  })
} else {
  new ContentScriptManager()
}

// 处理SPA路由变化
let lastUrl = location.href
new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    // 延迟重新初始化，等待页面渲染完成
    setTimeout(() => {
      new ContentScriptManager()
    }, 1000)
  }
}).observe(document, { subtree: true, childList: true })
