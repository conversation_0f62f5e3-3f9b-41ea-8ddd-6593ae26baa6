- [x] minifest配置说明
- [x] 项目页面划分：content注入内容，popup点击插件弹窗,以及option页面
- [x] React Router 6（HashRouter 适合插件）是什么?即插件项目没有后端，只能在前端刷新，只能使用`#路径`这种单页应用常用的路由方式。
- [x] 跨上下文通信...
- [x] lib/storage 共享类型 & 工具，消息封装、storage 封装提供的功能：
  - [x] 存储用户的配置信息
  - [x] 存储用户的提示词信息
  - [x] 存储用户的聊天记录信息
- [x] shadcn-ui相关知识了解
- [x] hooks useHook类似与useState,useEffect,useContext等，取代了单向数据流的繁琐设计，可以直接绑定storage完成存储操作，未来，直接用异步的网络同步取代。
- [x] storage推荐使用SQLite WASM + OPFS（Chrome 109+）。不限制磁盘容量，只要空间够的话。
- [x] Service Worker 生命周期。如何理解这个 冷启动 30s 后挂起 → 用 chrome.alarms 保活或 chrome.offscreen。
- [x] 调试