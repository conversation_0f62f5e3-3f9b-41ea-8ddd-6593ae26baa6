import React, { useState } from 'react'
import { useAppStore } from '@/stores/app-store'
import { MessagingService } from '@/lib/messaging'
import { MessageType } from '@/types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Zap, Plus, Star } from 'lucide-react'
import { formatDate, truncateText } from '@/lib/utils'

export function HomePage() {
  const { prompts, syncSettings, isLoading } = useAppStore()
  const [syncing, setSyncing] = useState(false)

  const recentPrompts = prompts.slice(0, 5)
  const favoritePrompts = prompts.filter(p => p.isFavorite).slice(0, 3)

  const handleQuickSync = async () => {
    if (!syncSettings.enabled) return
    
    setSyncing(true)
    try {
      const tab = await MessagingService.getCurrentTab()
      if (tab?.id) {
        await MessagingService.sendToContentScript(
          tab.id,
          MessageType.CAPTURE_PROMPT,
          {}
        )
      }
    } catch (error) {
      console.error('Quick sync error:', error)
    } finally {
      setSyncing(false)
    }
  }

  const handleUsePrompt = async (prompt: string) => {
    try {
      const tab = await MessagingService.getCurrentTab()
      if (tab?.id) {
        await MessagingService.sendToContentScript(
          tab.id,
          MessageType.INJECT_PROMPT,
          { prompt }
        )
      }
    } catch (error) {
      console.error('Use prompt error:', error)
    }
  }

  return (
    <div className="space-y-4">
      {/* 快速操作 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">快速操作</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button 
            onClick={handleQuickSync}
            disabled={syncing || !syncSettings.enabled}
            className="w-full"
            size="sm"
          >
            <Zap className="w-4 h-4 mr-2" />
            {syncing ? '同步中...' : '快速同步'}
          </Button>
          
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" className="flex-1">
              <Plus className="w-4 h-4 mr-1" />
              新建
            </Button>
            <Button variant="outline" size="sm" className="flex-1">
              <Star className="w-4 h-4 mr-1" />
              收藏
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 最近提示词 */}
      {recentPrompts.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">最近使用</CardTitle>
            <CardDescription className="text-xs">
              点击使用提示词
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            {recentPrompts.map((prompt) => (
              <div
                key={prompt.id}
                className="prompt-card"
                onClick={() => handleUsePrompt(prompt.content)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {truncateText(prompt.content, 50)}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`platform-badge ${prompt.platform}`}>
                        {prompt.platform}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatDate(prompt.timestamp)}
                      </span>
                    </div>
                  </div>
                  {prompt.isFavorite && (
                    <Star className="w-3 h-3 text-yellow-500 fill-current ml-2" />
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* 收藏提示词 */}
      {favoritePrompts.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">收藏夹</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {favoritePrompts.map((prompt) => (
              <div
                key={prompt.id}
                className="prompt-card"
                onClick={() => handleUsePrompt(prompt.content)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {truncateText(prompt.content, 50)}
                    </p>
                    <span className={`platform-badge ${prompt.platform} mt-1`}>
                      {prompt.platform}
                    </span>
                  </div>
                  <Star className="w-3 h-3 text-yellow-500 fill-current ml-2" />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* 空状态 */}
      {prompts.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <div className="text-muted-foreground">
              <Zap className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">还没有提示词记录</p>
              <p className="text-xs mt-1">
                在AI聊天页面输入提示词，插件会自动捕获
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
