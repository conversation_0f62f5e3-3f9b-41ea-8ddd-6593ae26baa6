import { DOMUtils } from './DOMUtils'

/**
 * 输入管理类
 * 从base.ts中提取的输入框管理相关方法
 */
export class InputManager {
  private inputElement: HTMLElement | null = null
  private sendButtonContainer: HTMLElement | null = null
  private lastInputValue: string = ''

  constructor() {}

  /**
   * 设置输入聚焦监听
   */
  setupInputFocusListener(selectors: { inputField: string }): void {
    // 监听全局点击事件
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      if (target && DOMUtils.isInputElement(target)) {
        // 检查是否匹配选择器
        if (target.matches(selectors.inputField)) {
          this.inputElement = target
          console.log('Input element focused:', target)
          
          // 触发输入框聚焦事件
          document.dispatchEvent(new CustomEvent('echosync:input-focused', { 
            detail: { inputElement: target } 
          }))
        }
      }
    })

    // 监听焦点事件
    document.addEventListener('focusin', (e) => {
      const target = e.target as HTMLElement
      if (target && target.matches(selectors.inputField)) {
        this.inputElement = target
        console.log('Input element focused via focusin:', target)
        
        // 触发输入框聚焦事件
        document.dispatchEvent(new CustomEvent('echosync:input-focused', { 
          detail: { inputElement: target } 
        }))
      }
    })

    console.log('Input focus listener set up')
  }

  /**
   * 查找并设置输入元素
   */
  async findAndSetupInputElement(selectors: { inputField: string }): Promise<HTMLElement | null> {
    // 首先尝试直接查找
    let inputElement = document.querySelector(selectors.inputField) as HTMLElement
    
    if (!inputElement) {
      // 如果没找到，尝试通用选择器
      const universalSelectors = DOMUtils.getUniversalInputSelectors()
      for (const selector of universalSelectors) {
        inputElement = document.querySelector(selector) as HTMLElement
        if (inputElement && DOMUtils.isVisibleElement(inputElement)) {
          break
        }
      }
    }

    if (!inputElement) {
      // 等待元素出现
      inputElement = await DOMUtils.waitForElement(selectors.inputField) as HTMLElement
    }

    if (inputElement) {
      this.inputElement = inputElement
      console.log('Input element found and set:', inputElement)
      
      // 设置输入监听
      this.setupInputMonitoring()
    }

    return inputElement
  }

  /**
   * 设置输入监听
   */
  private setupInputMonitoring(): void {
    if (!this.inputElement) return

    // 监听输入变化
    const handleInput = () => {
      const currentValue = this.getCurrentInput()
      if (currentValue !== this.lastInputValue) {
        this.lastInputValue = currentValue
        
        // 触发输入变化事件
        document.dispatchEvent(new CustomEvent('echosync:input-changed', {
          detail: { value: currentValue }
        }))
      }
    }

    this.inputElement.addEventListener('input', handleInput)
    this.inputElement.addEventListener('change', handleInput)
    this.inputElement.addEventListener('keyup', handleInput)

    console.log('Input monitoring set up')
  }

  /**
   * 设置发送监听
   */
  setupSendListener(selectors: { sendButton: string }): void {
    // 监听键盘事件（Enter键）
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey && this.inputElement && 
          document.activeElement === this.inputElement) {
        
        // 检查是否可以发送
        if (DOMUtils.canSendMessage(selectors)) {
          console.log('Send triggered by Enter key')
          this.handleSendEvent()
        }
      }
    })

    // 监听发送按钮点击
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      if (this.isSendButton(target, selectors.sendButton)) {
        console.log('Send triggered by button click')
        this.handleSendEvent()
      }
    })

    console.log('Send listener set up')
  }

  /**
   * 检查是否是发送按钮
   */
  private isSendButton(element: HTMLElement, sendButtonSelector: string): boolean {
    if (!element) return false

    // 检查元素本身
    if (element.matches(sendButtonSelector)) return true

    // 检查父元素（最多向上查找3层）
    let parent = element.parentElement
    let depth = 0
    while (parent && depth < 3) {
      if (parent.matches(sendButtonSelector)) return true
      parent = parent.parentElement
      depth++
    }

    // 检查常见的发送按钮特征
    const text = element.textContent?.toLowerCase() || ''
    const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || ''
    
    return text.includes('send') || text.includes('发送') ||
           ariaLabel.includes('send') || ariaLabel.includes('发送')
  }

  /**
   * 处理发送事件
   */
  private async handleSendEvent(): Promise<void> {
    // 获取发送前的输入内容
    const promptContent = this.getCurrentInput()
    
    if (promptContent && promptContent.trim().length > 0) {
      // 触发自动存档事件
      document.dispatchEvent(new CustomEvent('echosync:auto-archive', {
        detail: { prompt: promptContent.trim() }
      }))
    }
    
    // 延迟检查是否有新消息出现
    setTimeout(() => {
      document.dispatchEvent(new CustomEvent('echosync:check-new-message'))
    }, 1000)
  }

  /**
   * 获取当前输入内容
   */
  getCurrentInput(): string {
    if (!this.inputElement) return ''

    if (this.inputElement.tagName === 'TEXTAREA' || this.inputElement.tagName === 'INPUT') {
      return (this.inputElement as HTMLInputElement).value
    } else if (this.inputElement.contentEditable === 'true') {
      return this.inputElement.textContent || ''
    }

    return ''
  }

  /**
   * 注入提示词到输入框
   */
  injectPrompt(prompt: string): void {
    if (!this.inputElement) {
      console.warn('No input element found for prompt injection')
      return
    }

    DOMUtils.simulateUserInput(this.inputElement, prompt)
    console.log('Prompt injected:', prompt)
  }

  /**
   * 获取输入元素
   */
  getInputElement(): HTMLElement | null {
    return this.inputElement
  }

  /**
   * 获取发送按钮容器
   */
  getSendButtonContainer(): HTMLElement | null {
    return this.sendButtonContainer
  }

  /**
   * 设置发送按钮容器
   */
  setSendButtonContainer(container: HTMLElement): void {
    this.sendButtonContainer = container
  }

  /**
   * 获取最后输入值
   */
  getLastInputValue(): string {
    return this.lastInputValue
  }

  /**
   * 销毁
   */
  destroy(): void {
    this.inputElement = null
    this.sendButtonContainer = null
    this.lastInputValue = ''
  }
}
