import type { Prompt, Conversation, Settings, User } from '@/types'

export class StorageService {
  // Chrome Storage API 封装
  static async set<T>(key: string, value: T): Promise<void> {
    try {
      await chrome.storage.local.set({ [key]: value })
    } catch (error) {
      console.error('Storage set error:', error)
      throw error
    }
  }

  static async get<T>(key: string): Promise<T | null> {
    try {
      const result = await chrome.storage.local.get(key)
      return result[key] || null
    } catch (error) {
      console.error('Storage get error:', error)
      return null
    }
  }

  static async remove(key: string): Promise<void> {
    try {
      await chrome.storage.local.remove(key)
    } catch (error) {
      console.error('Storage remove error:', error)
      throw error
    }
  }

  static async clear(): Promise<void> {
    try {
      await chrome.storage.local.clear()
    } catch (error) {
      console.error('Storage clear error:', error)
      throw error
    }
  }

  // 专用方法
  static async getPrompts(): Promise<Prompt[]> {
    return (await this.get<Prompt[]>('prompts')) || []
  }

  static async savePrompts(prompts: Prompt[]): Promise<void> {
    await this.set('prompts', prompts)
  }

  static async addPrompt(prompt: Prompt): Promise<void> {
    const prompts = await this.getPrompts()
    prompts.unshift(prompt) // 添加到开头
    
    // 限制数量，避免存储过多
    if (prompts.length > 1000) {
      prompts.splice(1000)
    }
    
    await this.savePrompts(prompts)
  }

  static async getConversations(): Promise<Conversation[]> {
    return (await this.get<Conversation[]>('conversations')) || []
  }

  static async saveConversations(conversations: Conversation[]): Promise<void> {
    await this.set('conversations', conversations)
  }

  static async addConversation(conversation: Conversation): Promise<void> {
    const conversations = await this.getConversations()
    conversations.unshift(conversation)
    
    // 限制数量
    if (conversations.length > 100) {
      conversations.splice(100)
    }
    
    await this.saveConversations(conversations)
  }

  static async getSettings(): Promise<Settings> {
    const defaultSettings: Settings = {
      syncEnabled: true,
      autoSync: false,
      platforms: [
        {
          id: 'chatgpt',
          name: 'ChatGPT',
          url: 'https://chat.openai.com',
          enabled: true,
          selectors: {
            inputField: '#prompt-textarea',
            sendButton: '[data-testid="send-button"]',
            messageContainer: '[data-testid="conversation-turn"]'
          }
        },
        {
          id: 'deepseek',
          name: 'DeepSeek',
          url: 'https://chat.deepseek.com',
          enabled: true,
          selectors: {
            inputField: 'textarea[placeholder*="输入"]',
            sendButton: 'button[type="submit"]',
            messageContainer: '.message'
          }
        },
        {
          id: 'claude',
          name: 'Claude',
          url: 'https://claude.ai',
          enabled: true,
          selectors: {
            inputField: 'div[contenteditable="true"]',
            sendButton: 'button[aria-label="Send Message"]',
            messageContainer: '.font-claude-message'
          }
        }
      ],
      shortcuts: {
        openPopup: 'Ctrl+Shift+E',
        quickSync: 'Ctrl+Shift+S'
      },
      theme: 'system',
      language: 'zh'
    }

    return (await this.get<Settings>('settings')) || defaultSettings
  }

  static async saveSettings(settings: Settings): Promise<void> {
    await this.set('settings', settings)
  }

  static async getUser(): Promise<User | null> {
    return await this.get<User>('user')
  }

  static async saveUser(user: User): Promise<void> {
    await this.set('user', user)
  }

  static async removeUser(): Promise<void> {
    await this.remove('user')
  }

  // 数据导出/导入
  static async exportData(): Promise<{
    prompts: Prompt[]
    conversations: Conversation[]
    settings: Settings
  }> {
    const [prompts, conversations, settings] = await Promise.all([
      this.getPrompts(),
      this.getConversations(),
      this.getSettings()
    ])

    return { prompts, conversations, settings }
  }

  static async importData(data: {
    prompts?: Prompt[]
    conversations?: Conversation[]
    settings?: Settings
  }): Promise<void> {
    if (data.prompts) await this.savePrompts(data.prompts)
    if (data.conversations) await this.saveConversations(data.conversations)
    if (data.settings) await this.saveSettings(data.settings)
  }
}
